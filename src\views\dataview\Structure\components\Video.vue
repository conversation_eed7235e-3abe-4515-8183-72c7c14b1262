<template>
  <div class="w-full h-full flex flex-col relative" v-loading="isLoading">
    <video ref="videoPlayer" class="flex-1 bg-black w-full h-full" controls autoplay muted></video>
    <div class="absolute top-2 right-2 text-white bg-black bg-opacity-50 p-1 rounded">
      {{ label }}
    </div>
    <div class="absolute bottom-2 left-2">
      <el-button @click="emit('zoom', props.deviceSerial)" type="primary" circle>
        <el-icon>
          <Rank v-if="props.isZoomed" />
          <FullScreen v-else />
        </el-icon>
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, onBeforeUnmount } from 'vue';
import { getHikVideoUrl } from '/@/api/dataview/structure.ts';
import Hls from 'hls.js';
import { FullScreen, Rank } from '@element-plus/icons-vue';
import { storeToRefs } from 'pinia';
import { dataview } from '/@/stores/dataview';

const store = dataview();
const { structureCode: structureUniqueCode } = storeToRefs(store);

const props = defineProps({
  deviceSerial: {
    type: String,
    required: true,
  },
  type: {
    type: Number, required: true
  },
  label: {
    type: String,
  },
  isZoomed: {
    type: Boolean,


    default: false,
  },
});

const emit = defineEmits(['zoom']);

const isLoading = ref(false);
const videoPlayer = ref(null);
let hls = null;

// 检测视频URL类型
function isHlsUrl(url) {
  return url && (url.includes('.m3u8') || url.includes('hls'));
}

function isMp4Url(url) {
  return url && (url.includes('.mp4') || url.includes('.live.mp4'));
}

async function fetchVideoUrlAndPlay() {
  if (!props.deviceSerial || !videoPlayer.value) return;
  isLoading.value = true;
  try {
    const { data: videoUrl } = await getHikVideoUrl(props.deviceSerial, structureUniqueCode.value, props.type);

    if (!videoUrl) {
      console.error('Failed to get video URL');
      isLoading.value = false;
      return;
    }

    // 清理之前的HLS实例
    if (hls) {
      hls.destroy();
      hls = null;
    }

    // 根据URL类型选择播放方式
    if (isHlsUrl(videoUrl)) {
      // HLS流处理
      if (Hls.isSupported()) {
        hls = new Hls({
          debug: false,
        });
        hls.loadSource(videoUrl);
        hls.attachMedia(videoPlayer.value);
        hls.on(Hls.Events.MANIFEST_PARSED, () => {
          videoPlayer.value.play().catch(e => console.error("HLS play failed", e));
          isLoading.value = false;
        });
        hls.on(Hls.Events.ERROR, (_, data) => {
          if (data.fatal) {
            console.error('HLS fatal error:', data);
            isLoading.value = false;
          }
        });
      } else {
        console.error('HLS is not supported in this browser.');
        isLoading.value = false;
      }
    } else if (isMp4Url(videoUrl)) {
      // 直接MP4地址处理
      videoPlayer.value.src = videoUrl;
      videoPlayer.value.load();
      videoPlayer.value.addEventListener('loadeddata', () => {
        videoPlayer.value.play().catch(e => console.error("MP4 play failed", e));
        isLoading.value = false;
      }, { once: true });
      videoPlayer.value.addEventListener('error', (e) => {
        console.error('MP4 load error:', e);
        isLoading.value = false;
      }, { once: true });
    } else {
      // 尝试作为普通视频URL处理
      videoPlayer.value.src = videoUrl;
      videoPlayer.value.load();
      videoPlayer.value.addEventListener('loadeddata', () => {
        videoPlayer.value.play().catch(e => console.error("Video play failed", e));
        isLoading.value = false;
      }, { once: true });
      videoPlayer.value.addEventListener('error', (e) => {
        console.error('Video load error:', e);
        isLoading.value = false;
      }, { once: true });
    }
  } catch (error) {
    console.error('Exception while fetching video URL:', error);
    isLoading.value = false;
  }
}

onMounted(fetchVideoUrlAndPlay);

watch(() => props.deviceSerial, (newVal, oldVal) => {
  if (newVal && newVal !== oldVal) {
    fetchVideoUrlAndPlay();
  }
});

onBeforeUnmount(() => {
  if (hls) {
    hls.destroy();
  }
});
</script>

<style scoped>
#player {
  background-color: #000;
  /* 占位符颜色 */
  width: 100%;
  /* 默认为容器的全宽 */
  aspect-ratio: 16 / 9;
  /* 保持16:9宽高比 */
  max-width: 800px;
  /* 可选：较大屏幕的最大宽度 */
  margin: 0 auto;
  /* 如果使用max-width，则使播放器居中 */
}

/* 隐藏原生视频控件（如果出现） */
:deep(::-webkit-media-controls) {
  display: none !important;
}
</style>
